# تعليمات بناء تطبيق العقل المبدع Android

## 🚨 حل مشكلة Gradle

إذا واجهت خطأ `Could not find or load main class org.gradle.wrapper.GradleWrapperMain`، اتبع هذه الخطوات:

### الحل الأول: إعادة تحميل Gradle Wrapper

```bash
# 1. احذف ملف gradle-wrapper.jar التالف
cd android
del gradle\wrapper\gradle-wrapper.jar

# 2. حمل ملف جديد
curl -o gradle\wrapper\gradle-wrapper.jar https://github.com/gradle/gradle/raw/v8.10.2/gradle/wrapper/gradle-wrapper.jar

# 3. ارجع للمجلد الرئيسي
cd ..

# 4. جرب البناء مرة أخرى
flutter build apk --release
```

### الحل الثاني: استخدام Android Studio

1. افتح Android Studio
2. اختر "Open an existing project"
3. اختر مجلد `creative_mind_app`
4. انتظر حتى يتم تحميل المشروع
5. من القائمة: Build → Build Bundle(s) / APK(s) → Build APK(s)

### الحل الثالث: إعادة إنشاء مجلد Android

```bash
# 1. احذف مجلد android
rmdir /s android

# 2. أعد إنشاؤه
flutter create --platforms android .

# 3. أعد تطبيق التعديلات على AndroidManifest.xml
# (راجع الملف الحالي للتعديلات المطلوبة)

# 4. جرب البناء
flutter build apk --release
```

## 📱 طرق بناء التطبيق

### 1. بناء APK للتطوير
```bash
flutter build apk --debug
```

### 2. بناء APK للإنتاج
```bash
flutter build apk --release
```

### 3. بناء App Bundle للنشر
```bash
flutter build appbundle --release
```

### 4. تشغيل التطبيق على جهاز متصل
```bash
flutter run
```

## 📁 مواقع الملفات المبنية

- **APK Debug**: `build/app/outputs/flutter-apk/app-debug.apk`
- **APK Release**: `build/app/outputs/flutter-apk/app-release.apk`
- **App Bundle**: `build/app/outputs/bundle/release/app-release.aab`

## ✅ التحقق من نجاح البناء

بعد البناء الناجح، ستجد رسالة مثل:
```
✓ Built build/app/outputs/flutter-apk/app-release.apk (XX.XMB).
```

## 🔧 نصائح إضافية

1. **تأكد من اتصال الإنترنت** أثناء أول بناء
2. **أغلق Android Studio** إذا كان مفتوحاً أثناء البناء
3. **استخدم PowerShell كمدير** للحصول على صلاحيات كاملة
4. **تأكد من وجود مساحة كافية** على القرص الصلب (على الأقل 2GB)

## 📞 في حالة استمرار المشاكل

إذا استمرت المشاكل، جرب:

```bash
# تنظيف المشروع
flutter clean

# إعادة تحميل dependencies
flutter pub get

# إعادة البناء
flutter build apk --release
```

أو استخدم Android Studio لبناء التطبيق مباشرة.
