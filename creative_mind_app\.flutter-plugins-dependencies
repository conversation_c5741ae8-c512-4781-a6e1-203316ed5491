{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "cloud_firestore", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\cloud_firestore-4.17.5\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_auth-4.16.0\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-2.32.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "webview_flutter_wkwebview", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\webview_flutter_wkwebview-3.22.0\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "cloud_firestore", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\cloud_firestore-4.17.5\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_auth-4.16.0\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-2.32.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_android-2.2.17\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_android-2.4.10\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "webview_flutter_android", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\webview_flutter_android-4.7.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "cloud_firestore", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\cloud_firestore-4.17.5\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_auth-4.16.0\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-2.32.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_foundation-2.4.1\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_foundation-2.5.4\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "webview_flutter_wkwebview", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\webview_flutter_wkwebview-3.22.0\\\\", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "path_provider_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_linux-2.2.1\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_linux-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}], "windows": [{"name": "cloud_firestore", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\cloud_firestore-4.17.5\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_auth-4.16.0\\\\", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core-2.32.0\\\\", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\path_provider_windows-2.3.0\\\\", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_windows-2.4.1\\\\", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}], "web": [{"name": "cloud_firestore_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\cloud_firestore_web-3.12.5\\\\", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_auth_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_auth_web-5.8.13\\\\", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_core_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\firebase_core_web-2.23.0\\\\", "dependencies": [], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Pub\\\\Cache\\\\hosted\\\\pub.dev\\\\shared_preferences_web-2.4.3\\\\", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "cloud_firestore", "dependencies": ["cloud_firestore_web", "firebase_core"]}, {"name": "cloud_firestore_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_auth", "dependencies": ["firebase_auth_web", "firebase_core"]}, {"name": "firebase_auth_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "webview_flutter", "dependencies": ["webview_flutter_android", "webview_flutter_wkwebview"]}, {"name": "webview_flutter_android", "dependencies": []}, {"name": "webview_flutter_wkwebview", "dependencies": []}], "date_created": "2025-07-01 03:46:36.233004", "version": "3.29.3", "swift_package_manager_enabled": {"ios": false, "macos": false}}