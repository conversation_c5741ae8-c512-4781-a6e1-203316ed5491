# العقل المبدع - تطبيق Android WebView

تطبيق Android متكامل يعرض محتوى "العقل المبدع" باستخدام WebView مع تحسينات خاصة للجوال.

## المميزات

- ✅ تطبيق Android WebView متكامل
- ✅ عرض محتوى HTML الكامل للعقل المبدع
- ✅ تحسين كامل للجوال
- ✅ واجهة مستخدم عربية
- ✅ شاشة تحميل مخصصة
- ✅ دعم كامل للإنترنت
- ✅ أيقونة تطبيق مخصصة

## كيفية البناء والتشغيل

### متطلبات النظام
- Flutter SDK (الإصدار 3.29.3 أو أحدث)
- Android Studio
- Android SDK

### خطوات التشغيل

1. **تحديث Dependencies:**
```bash
flutter pub get
```

2. **تشغيل التطبيق في وضع التطوير:**
```bash
flutter run
```

3. **بناء APK للإنتاج:**
```bash
flutter build apk --release
```

4. **بناء App Bundle للنشر في Google Play:**
```bash
flutter build appbundle --release
```

## بنية المشروع

```
creative_mind_app/
├── lib/
│   └── main.dart              # الكود الرئيسي للتطبيق
├── assets/
│   ├── html/
│   │   └── index.html         # ملف HTML الرئيسي للعقل المبدع
│   ├── images/               # الصور والأيقونات
│   └── icons/                # أيقونات إضافية
├── android/                  # إعدادات Android
└── pubspec.yaml             # إعدادات المشروع والـ dependencies
```

## التخصيص

### تغيير المحتوى
- قم بتعديل ملف `assets/html/index.html` لتغيير محتوى التطبيق

### تغيير الأيقونة
1. ضع أيقونة جديدة في `assets/images/app_icon.png` (1024x1024 بكسل)
2. قم بإلغاء التعليق عن قسم `flutter_launcher_icons` في `pubspec.yaml`
3. شغل الأمر: `flutter pub run flutter_launcher_icons:main`

### تغيير اسم التطبيق
- عدل `android/app/src/main/AndroidManifest.xml`
- غير قيمة `android:label`

## الاختبار

```bash
flutter test
```

## المساهمة

هذا التطبيق مصمم خصيصاً لعرض محتوى "العقل المبدع" في تطبيق Android متكامل.
